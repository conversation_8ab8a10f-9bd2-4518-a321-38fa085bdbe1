{"name": "po-management-system", "version": "1.0.0", "description": "PO-Based Data Entry and History Tracking Web Application", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client"}, "keywords": ["MERN", "PO Management", "Manufacturing"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^8.2.2"}}