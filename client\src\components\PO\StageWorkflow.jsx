import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Alert,
  Card,
  CardContent,
  Grid,
  Chip,
  Fab
} from '@mui/material';
import { ArrowBack, Add } from '@mui/icons-material';
import axios from 'axios';

// Import stage components
import RequirementForm from './stages/RequirementForm';
import ExtrusionProductionForm from './stages/ExtrusionProductionForm';
import PrintingForm from './stages/PrintingForm';
import CuttingSealingForm from './stages/CuttingSealingForm';
import PunchForm from './stages/PunchForm';
import PackagingDispatchForm from './stages/PackagingDispatchForm';

const StageWorkflow = () => {
  const { poId, stage } = useParams();
  const navigate = useNavigate();
  
  const [po, setPO] = useState(null);
  const [availableMachines, setAvailableMachines] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    fetchPOData();
    fetchAvailableMachines();
  }, [poId]);

  const fetchPOData = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/pos/${poId}`);
      setPO(response.data);
    } catch (error) {
      console.error('Error fetching PO:', error);
      setError('Failed to fetch PO data');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableMachines = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/pos/${poId}/available-machines`);
      setAvailableMachines(response.data.availableMachines);
    } catch (error) {
      console.error('Error fetching available machines:', error);
    }
  };

  const handleMachineAdd = async (formData) => {
    try {
      setError('');
      
      const response = await axios.post(
        `http://localhost:5000/api/pos/${poId}/machines/${stage}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      console.log('Machine added successfully:', response.data);
      
      // Refresh data
      await fetchPOData();
      await fetchAvailableMachines();
      
      // Hide form
      setShowAddForm(false);
      
    } catch (error) {
      console.error('Error adding machine:', error);
      setError('Failed to add machine: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleCompleteStage = async () => {
    try {
      setError('');
      
      const response = await axios.put(`http://localhost:5000/api/pos/${poId}/complete-stage`);
      
      console.log('Stage completed:', response.data);
      
      // Navigate back to dashboard
      navigate('/dashboard');
      
    } catch (error) {
      console.error('Error completing stage:', error);
      setError('Failed to complete stage: ' + (error.response?.data?.message || error.message));
    }
  };

  const renderStageForm = () => {
    const commonProps = {
      onComplete: handleMachineAdd,
      availableMachines: availableMachines,
    };

    switch (stage) {
      case 'requirement':
        return <RequirementForm {...commonProps} />;
      case 'extrusion':
        return <ExtrusionProductionForm {...commonProps} />;
      case 'printing':
        return <PrintingForm {...commonProps} />;
      case 'cutting':
        return <CuttingSealingForm {...commonProps} />;
      case 'punch':
        return <PunchForm {...commonProps} />;
      case 'packaging':
        return <PackagingDispatchForm {...commonProps} />;
      default:
        return <Typography>Invalid stage</Typography>;
    }
  };

  const getStageDisplayName = (stageName) => {
    const stageNames = {
      requirement: 'Requirement',
      extrusion: 'Extrusion Production',
      printing: 'Printing',
      cutting: 'Cutting & Sealing',
      punch: 'Punch',
      packaging: 'Packaging & Dispatch'
    };
    return stageNames[stageName] || stageName;
  };

  if (loading) {
    return <Typography>Loading...</Typography>;
  }

  if (!po) {
    return <Typography>PO not found</Typography>;
  }

  // Check if current stage matches the URL stage
  if (po.currentStage !== stage) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          This stage is not currently active. Current stage: {po.currentStageDisplay || getStageDisplayName(po.currentStage)}
        </Alert>
        <Button onClick={() => navigate('/dashboard')} sx={{ mt: 2 }}>
          Back to Dashboard
        </Button>
      </Container>
    );
  }

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => navigate('/dashboard')}
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {po.poNumber} - {getStageDisplayName(stage)}
          </Typography>
          <Chip 
            label={`Stage: ${getStageDisplayName(stage)}`} 
            color="primary" 
            variant="outlined"
          />
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        {/* Current machines for this stage */}
        <Typography variant="h5" gutterBottom>
          Machines Added ({po.machines.length}/6)
        </Typography>

        <Grid container spacing={2} sx={{ mb: 3 }}>
          {po.machines.map((machine) => (
            <Grid item xs={12} sm={6} md={4} key={machine._id}>
              <Card>
                <CardContent>
                  <Typography variant="h6">
                    Machine {machine.machineNo}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Stages completed: {machine.completedStages.length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Add machine button */}
        {availableMachines.length > 0 && !showAddForm && (
          <Box sx={{ mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowAddForm(true)}
              size="large"
            >
              Add Machine
            </Button>
          </Box>
        )}

        {/* Add machine form */}
        {showAddForm && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Add New Machine
            </Typography>
            {renderStageForm()}
            <Button 
              variant="outlined" 
              onClick={() => setShowAddForm(false)}
              sx={{ mt: 2 }}
            >
              Cancel
            </Button>
          </Box>
        )}

        {/* Complete stage button */}
        {po.machines.length > 0 && (
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Button
              variant="contained"
              color="success"
              size="large"
              onClick={handleCompleteStage}
            >
              Complete {getStageDisplayName(stage)} Stage
            </Button>
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              This will advance the PO to the next stage and return to dashboard
            </Typography>
          </Box>
        )}

        {/* Floating action button for quick add */}
        {availableMachines.length > 0 && !showAddForm && (
          <Fab
            color="primary"
            aria-label="add machine"
            sx={{ position: 'fixed', bottom: 16, right: 16 }}
            onClick={() => setShowAddForm(true)}
          >
            <Add />
          </Fab>
        )}
      </Container>
    </>
  );
};

export default StageWorkflow;
