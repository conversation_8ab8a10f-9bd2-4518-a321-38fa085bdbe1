import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Alert,
  Card,
  CardContent,
  Grid,
  Chip,
  Fab
} from '@mui/material';
import { ArrowBack, Add, CheckCircle, RadioButtonUnchecked, Edit } from '@mui/icons-material';
import axios from 'axios';

// Import stage components
import RequirementForm from './stages/RequirementForm';
import ExtrusionProductionForm from './stages/ExtrusionProductionForm';
import PrintingForm from './stages/PrintingForm';
import CuttingSealingForm from './stages/CuttingSealingForm';
import PunchForm from './stages/PunchForm';
import PackagingDispatchForm from './stages/PackagingDispatchForm';

const StageWorkflow = () => {
  const { poId, stage } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const machineIdFromUrl = searchParams.get('machineId');

  const [po, setPO] = useState(null);
  const [availableMachines, setAvailableMachines] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingMachine, setEditingMachine] = useState(null);

  useEffect(() => {
    fetchPOData();
    fetchAvailableMachines();
  }, [poId]);

  useEffect(() => {
    // If machineId is provided in URL, automatically open edit form for that machine
    if (machineIdFromUrl && po) {
      const machine = po.machines.find(m => m._id === machineIdFromUrl);
      if (machine) {
        setEditingMachine(machine);
        setShowAddForm(true);
        console.log('Auto-opening form for machine:', machine.machineNo);
      }
    }
  }, [machineIdFromUrl, po]);

  const fetchPOData = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/pos/${poId}`);
      setPO(response.data);
    } catch (error) {
      console.error('Error fetching PO:', error);
      setError('Failed to fetch PO data');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableMachines = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/pos/${poId}/available-machines`);
      setAvailableMachines(response.data.availableMachines);
    } catch (error) {
      console.error('Error fetching available machines:', error);
    }
  };

  const handleMachineAdd = async (formData) => {
    try {
      setError('');
      console.log('handleMachineAdd called with:', { editingMachine, stage, formData });

      // Map URL stage names to database stage names
      const stageMapping = {
        'requirement': 'requirement',
        'extrusion': 'extrusionProduction',
        'printing': 'printing',
        'cutting': 'cuttingSealing',
        'punch': 'punch',
        'packaging': 'packagingDispatch'
      };

      const dbStageName = stageMapping[stage] || stage;
      console.log('Stage mapping:', { urlStage: stage, dbStage: dbStageName });

      if (editingMachine) {
        // Convert FormData to regular object for PUT request
        const dataObject = {};
        if (formData instanceof FormData) {
          for (let [key, value] of formData.entries()) {
            dataObject[key] = value;
          }
        } else {
          Object.assign(dataObject, formData);
        }

        console.log('Updating machine with data:', dataObject);

        // Update existing machine - use database stage name
        const response = await axios.put(
          `http://localhost:5000/api/pos/${poId}/machines/${editingMachine._id}/stages/${dbStageName}`,
          dataObject,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
        console.log('Machine updated successfully:', response.data);
      } else {
        // Add new machine - use URL stage name for route
        const response = await axios.post(
          `http://localhost:5000/api/pos/${poId}/machines/${stage}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );
        console.log('Machine added successfully:', response.data);
      }

      // Refresh data
      await fetchPOData();
      await fetchAvailableMachines();

      // Hide form and reset editing state
      setShowAddForm(false);
      setEditingMachine(null);

      // Navigate back to dashboard after successful update
      if (editingMachine) {
        console.log('Navigating back to dashboard after update');
        navigate('/dashboard');
      }

    } catch (error) {
      console.error('Error saving machine:', error);
      setError('Failed to save machine: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleCompleteStage = async () => {
    try {
      setError('');

      const response = await axios.put(`http://localhost:5000/api/pos/${poId}/complete-stage`);

      console.log('Stage completed:', response.data);

      // Navigate back to dashboard
      navigate('/dashboard');

    } catch (error) {
      console.error('Error completing stage:', error);
      setError('Failed to complete stage: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleCompleteMachineStage = async (machineId, stageName) => {
    try {
      setError('');

      // Map stage names to match API expectations
      const stageMapping = {
        'requirement': 'requirement',
        'extrusion': 'extrusionProduction',
        'printing': 'printing',
        'cutting': 'cuttingSealing',
        'punch': 'punch',
        'packaging': 'packagingDispatch'
      };

      const apiStage = stageMapping[stage] || stage;

      const response = await axios.put(
        `http://localhost:5000/api/pos/${poId}/machines/${machineId}/complete-stage/${apiStage}`
      );

      console.log('Machine stage completed:', response.data);

      // Refresh PO data
      await fetchPOData();

    } catch (error) {
      console.error('Error completing machine stage:', error);
      setError('Failed to complete machine stage: ' + (error.response?.data?.message || error.message));
    }
  };

  const renderStageForm = () => {
    // Get existing data for the stage being edited
    const getExistingData = () => {
      if (!editingMachine) return null;

      const stageDataMapping = {
        'requirement': editingMachine.requirement,
        'extrusion': editingMachine.extrusionProduction,
        'printing': editingMachine.printing,
        'cutting': editingMachine.cuttingSealing,
        'punch': editingMachine.punch,
        'packaging': editingMachine.packagingDispatch
      };

      return stageDataMapping[stage];
    };

    const commonProps = {
      onComplete: handleMachineAdd,
      availableMachines: editingMachine ? [editingMachine.machineNo] : availableMachines,
      initialData: getExistingData(),
      isEditing: !!editingMachine,
      machineNo: editingMachine?.machineNo
    };

    switch (stage) {
      case 'requirement':
        return <RequirementForm {...commonProps} />;
      case 'extrusion':
        return <ExtrusionProductionForm {...commonProps} />;
      case 'printing':
        return <PrintingForm {...commonProps} />;
      case 'cutting':
        return <CuttingSealingForm {...commonProps} />;
      case 'punch':
        return <PunchForm {...commonProps} />;
      case 'packaging':
        return <PackagingDispatchForm {...commonProps} />;
      default:
        return <Typography>Invalid stage</Typography>;
    }
  };

  const getStageDisplayName = (stageName) => {
    const stageNames = {
      requirement: 'Requirement',
      extrusion: 'Extrusion Production',
      printing: 'Printing',
      cutting: 'Cutting & Sealing',
      punch: 'Punch',
      packaging: 'Packaging & Dispatch'
    };
    return stageNames[stageName] || stageName;
  };

  const getNextIncompleteStage = (machine) => {
    const stageOrder = [
      { key: 'requirement', route: 'requirement' },
      { key: 'extrusionProduction', route: 'extrusion' },
      { key: 'printing', route: 'printing' },
      { key: 'cuttingSealing', route: 'cutting' },
      { key: 'punch', route: 'punch' },
      { key: 'packagingDispatch', route: 'packaging' }
    ];

    for (const stage of stageOrder) {
      if (!machine.completedStages.includes(stage.key)) {
        return stage;
      }
    }
    return null; // All stages completed
  };

  const handleEditMachine = (machine) => {
    const nextStage = getNextIncompleteStage(machine);
    console.log('handleEditMachine called:', { machine, nextStage });
    if (nextStage) {
      const navigationPath = `/po/${poId}/stage/${nextStage.route}?machineId=${machine._id}`;
      console.log('Navigating to:', navigationPath);
      navigate(navigationPath);
    } else {
      setError('All stages completed for this machine');
    }
  };

  if (loading) {
    return <Typography>Loading...</Typography>;
  }

  if (!po) {
    return <Typography>PO not found</Typography>;
  }

  // Allow access to any stage for individual machine progression
  // Removed the restriction that only allowed current PO stage

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => navigate('/dashboard')}
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {po.poNumber} - {getStageDisplayName(stage)}
          </Typography>
          <Chip 
            label={`Stage: ${getStageDisplayName(stage)}`} 
            color="primary" 
            variant="outlined"
          />
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        {/* Current machines for this stage */}
        <Typography variant="h5" gutterBottom>
          Machines Added ({po.machines.length}/6)
        </Typography>

        <Grid container spacing={2} sx={{ mb: 3 }}>
          {po.machines.map((machine) => {
            const nextStage = getNextIncompleteStage(machine);
            const allStagesCompleted = machine.completedStages.length === 6;

            return (
              <Grid item xs={12} sm={6} md={4} key={machine._id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="h6">
                        Machine {machine.machineNo}
                      </Typography>
                      {allStagesCompleted ? (
                        <Chip
                          label="All Completed"
                          color="success"
                          size="small"
                          icon={<CheckCircle />}
                        />
                      ) : (
                        <Chip
                          label={`Next: ${nextStage ? getStageDisplayName(nextStage.route) : 'None'}`}
                          color="info"
                          size="small"
                          icon={<RadioButtonUnchecked />}
                        />
                      )}
                    </Box>

                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      Stages completed: {machine.completedStages.length}/6
                    </Typography>

                    {/* Show completed stages */}
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="textSecondary">
                        Progress:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {['requirement', 'extrusionProduction', 'printing', 'cuttingSealing', 'punch', 'packagingDispatch'].map(stageName => {
                          const isCompleted = machine.completedStages.includes(stageName);
                          const stageDisplayNames = {
                            'requirement': 'Req',
                            'extrusionProduction': 'Ext',
                            'printing': 'Print',
                            'cuttingSealing': 'Cut',
                            'punch': 'Punch',
                            'packagingDispatch': 'Pack'
                          };

                          return (
                            <Chip
                              key={stageName}
                              label={stageDisplayNames[stageName]}
                              size="small"
                              color={isCompleted ? 'success' : 'default'}
                              variant={isCompleted ? 'filled' : 'outlined'}
                              sx={{ fontSize: '0.7rem', height: 20 }}
                            />
                          );
                        })}
                      </Box>
                    </Box>

                    {/* Edit button to continue from next incomplete stage */}
                    {!allStagesCompleted && nextStage && (
                      <Button
                        variant="contained"
                        size="small"
                        fullWidth
                        startIcon={<Edit />}
                        sx={{ mt: 1 }}
                        onClick={() => handleEditMachine(machine)}
                      >
                        Continue: {getStageDisplayName(nextStage.route)}
                      </Button>
                    )}

                    {allStagesCompleted && (
                      <Button
                        variant="outlined"
                        size="small"
                        fullWidth
                        disabled
                        sx={{ mt: 1 }}
                      >
                        ✓ All Stages Completed
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        {/* Add machine button */}
        {availableMachines.length > 0 && !showAddForm && (
          <Box sx={{ mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowAddForm(true)}
              size="large"
            >
              Add Machine
            </Button>
          </Box>
        )}

        {/* Add/Edit machine form */}
        {showAddForm && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {editingMachine
                ? `Edit Machine ${editingMachine.machineNo} - ${getStageDisplayName(stage)}`
                : `Add New Machine - ${getStageDisplayName(stage)}`
              }
            </Typography>
            {renderStageForm()}
            <Button
              variant="outlined"
              onClick={() => {
                setShowAddForm(false);
                setEditingMachine(null);
              }}
              sx={{ mt: 2 }}
            >
              Cancel
            </Button>
          </Box>
        )}

        {/* Information about continuing workflow */}
        {po.machines.length > 0 && (
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="body1" color="textSecondary">
              Use the "Continue" button on each machine to proceed to the next incomplete stage
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              Each machine can progress independently through all 6 stages
            </Typography>
          </Box>
        )}

        {/* Floating action button for quick add */}
        {availableMachines.length > 0 && !showAddForm && (
          <Fab
            color="primary"
            aria-label="add machine"
            sx={{ position: 'fixed', bottom: 16, right: 16 }}
            onClick={() => setShowAddForm(true)}
          >
            <Add />
          </Fab>
        )}
      </Container>
    </>
  );
};

export default StageWorkflow;
