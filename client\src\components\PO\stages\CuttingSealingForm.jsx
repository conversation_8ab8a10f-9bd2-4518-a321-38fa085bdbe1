import React, { useState } from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Grid,
  Alert
} from '@mui/material';

const CuttingSealingForm = ({ onComplete, onBack, machineData }) => {
  const [formData, setFormData] = useState({
    machineNo: '',
    size: '',
    operatorName: '',
    heating1: '',
    heating2: '',
    noOfRolls: '',
    cuttingWaste: '',
    printWaste: '',
    kgs: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const submitData = {
        ...formData,
        machineNo: formData.machineNo ? parseInt(formData.machineNo) : null,
        heating1: formData.heating1 ? parseFloat(formData.heating1) : null,
        heating2: formData.heating2 ? parseFloat(formData.heating2) : null,
        noOfRolls: formData.noOfRolls ? parseInt(formData.noOfRolls) : null,
        cuttingWaste: formData.cuttingWaste ? parseFloat(formData.cuttingWaste) : null,
        printWaste: formData.printWaste ? parseFloat(formData.printWaste) : null,
        kgs: formData.kgs ? parseFloat(formData.kgs) : null
      };

      await onComplete(submitData);
    } catch (error) {
      setError('Failed to save cutting & sealing data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 4 }}>
      <Typography variant="h5" gutterBottom>
        Stage 4: Cutting & Sealing
      </Typography>
      
      {machineData && (
        <Typography variant="subtitle1" color="primary" gutterBottom>
          Machine {machineData.machineNo}
        </Typography>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Machine No."
              name="machineNo"
              type="number"
              value={formData.machineNo}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Size"
              name="size"
              value={formData.size}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Operator Name"
              name="operatorName"
              value={formData.operatorName}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Heating 1"
              name="heating1"
              type="number"
              value={formData.heating1}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Heating 2"
              name="heating2"
              type="number"
              value={formData.heating2}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="No. of Rolls"
              name="noOfRolls"
              type="number"
              value={formData.noOfRolls}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Cutting Waste"
              name="cuttingWaste"
              type="number"
              value={formData.cuttingWaste}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Print Waste"
              name="printWaste"
              type="number"
              value={formData.printWaste}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Kgs"
              name="kgs"
              type="number"
              value={formData.kgs}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            onClick={onBack}
            disabled={loading}
            size="large"
          >
            Back
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            size="large"
          >
            {loading ? 'Saving...' : 'Next: Punch'}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default CuttingSealingForm;
