import React, { useState } from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Grid,
  Alert
} from '@mui/material';

const PrintingForm = ({ onComplete, onBack, machineData }) => {
  const [formData, setFormData] = useState({
    machineNo: '',
    size: '',
    operatorName: '',
    noOfRolls: '',
    waste: '',
    kgs: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    console.log('PrintingForm - Form data:', formData);

    try {
      const submitData = {
        ...formData,
        machineNo: formData.machineNo ? parseInt(formData.machineNo) : null,
        noOfRolls: formData.noOfRolls ? parseInt(formData.noOfRolls) : null,
        waste: formData.waste ? parseFloat(formData.waste) : null,
        kgs: formData.kgs ? parseFloat(formData.kgs) : null
      };

      console.log('PrintingForm - Submitting data:', submitData);

      if (onComplete) {
        await onComplete(submitData);
        console.log('PrintingForm - Data submitted successfully');
      } else {
        throw new Error('onComplete function not provided');
      }
    } catch (error) {
      console.error('PrintingForm - Submit error:', error);
      setError('Failed to save printing data: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 4 }}>
      <Typography variant="h5" gutterBottom>
        Stage 3: Printing
      </Typography>
      
      {machineData && (
        <Typography variant="subtitle1" color="primary" gutterBottom>
          Machine {machineData.machineNo}
        </Typography>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Machine No."
              name="machineNo"
              type="number"
              value={formData.machineNo}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Size"
              name="size"
              value={formData.size}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Operator Name"
              name="operatorName"
              value={formData.operatorName}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="No. of Rolls"
              name="noOfRolls"
              type="number"
              value={formData.noOfRolls}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Waste"
              name="waste"
              type="number"
              value={formData.waste}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Kgs"
              name="kgs"
              type="number"
              value={formData.kgs}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            onClick={onBack}
            disabled={loading}
            size="large"
          >
            Back
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            size="large"
          >
            {loading ? 'Saving...' : 'Next: Cutting & Sealing'}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default PrintingForm;
