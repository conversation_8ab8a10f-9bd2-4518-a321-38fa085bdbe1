const express = require('express');
const multer = require('multer');
const path = require('path');
const PDFDocument = require('pdfkit');
const fs = require('fs');
const PO = require('../models/PO');
const Counter = require('../models/Counter');
const auth = require('../middleware/auth');

const router = express.Router();

// Configure multer for image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Helper function to get next PO number
const getNextPONumber = async () => {
  try {
    const counter = await Counter.findByIdAndUpdate(
      'po_sequence',
      { $inc: { sequence_value: 1 } },
      { new: true, upsert: true }
    );
    return `PO-${counter.sequence_value}`;
  } catch (error) {
    throw new Error('Failed to generate PO number');
  }
};

// Create new PO
router.post('/create', auth, async (req, res) => {
  try {
    const poNumber = await getNextPONumber();
    
    const newPO = new PO({
      poNumber,
      createdBy: req.user._id,
      machines: [],
      status: 'draft'
    });

    await newPO.save();

    res.status(201).json({
      message: 'PO created successfully',
      po: newPO
    });
  } catch (error) {
    console.error('Create PO error:', error);
    res.status(500).json({ message: 'Failed to create PO' });
  }
});

// Get all POs for the authenticated user
router.get('/', auth, async (req, res) => {
  try {
    const { search } = req.query;
    let query = { createdBy: req.user._id };

    if (search) {
      query.poNumber = { $regex: search, $options: 'i' };
    }

    const pos = await PO.find(query)
      .sort({ createdAt: -1 })
      .select('poNumber createdAt status machines isFinalized');

    res.json(pos);
  } catch (error) {
    console.error('Get POs error:', error);
    res.status(500).json({ message: 'Failed to fetch POs' });
  }
});

// Get specific PO by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const po = await PO.findOne({
      _id: req.params.id,
      createdBy: req.user._id
    });

    if (!po) {
      return res.status(404).json({ message: 'PO not found' });
    }

    res.json(po);
  } catch (error) {
    console.error('Get PO error:', error);
    res.status(500).json({ message: 'Failed to fetch PO' });
  }
});

// Get available machine numbers for a PO
router.get('/:id/available-machines', auth, async (req, res) => {
  try {
    const po = await PO.findOne({
      _id: req.params.id,
      createdBy: req.user._id
    });

    if (!po) {
      return res.status(404).json({ message: 'PO not found' });
    }

    const availableMachines = po.getAvailableMachineNumbers();
    const canAddMore = po.canAddMoreMachines();

    res.json({
      availableMachines,
      canAddMore,
      currentMachineCount: po.machines.length
    });
  } catch (error) {
    console.error('Get available machines error:', error);
    res.status(500).json({ message: 'Failed to fetch available machines' });
  }
});

// Add machine to PO with requirement stage
router.post('/:id/machines', auth, upload.single('image'), async (req, res) => {
  try {
    const po = await PO.findOne({
      _id: req.params.id,
      createdBy: req.user._id
    });

    if (!po) {
      return res.status(404).json({ message: 'PO not found' });
    }

    if (!po.canAddMoreMachines()) {
      return res.status(400).json({ message: 'Maximum 6 machines allowed per PO' });
    }

    const { machineNo, size, micron, bagType, quantity, print, color, packagingType, material } = req.body;

    // Check if machine number is already used in this PO
    const existingMachine = po.machines.find(m => m.machineNo === parseInt(machineNo));
    if (existingMachine) {
      return res.status(400).json({ message: 'Machine number already used in this PO' });
    }

    const machineEntry = {
      machineNo: parseInt(machineNo),
      requirement: {
        machineNo: parseInt(machineNo),
        size,
        micron,
        bagType,
        quantity: parseInt(quantity),
        print,
        color,
        packagingType,
        material,
        image: req.file ? req.file.filename : null
      },
      completedStages: ['requirement']
    };

    po.machines.push(machineEntry);
    po.status = 'in-progress';
    await po.save();

    // Get the newly added machine with its MongoDB _id
    const savedMachine = po.machines[po.machines.length - 1];

    res.status(201).json({
      message: 'Machine added successfully',
      machine: savedMachine
    });
  } catch (error) {
    console.error('Add machine error:', error);
    res.status(500).json({ message: 'Failed to add machine' });
  }
});

// Update machine stage
router.put('/:poId/machines/:machineId/stages/:stage', auth, async (req, res) => {
  try {
    const { poId, machineId, stage } = req.params;
    const stageData = req.body;

    const po = await PO.findOne({
      _id: poId,
      createdBy: req.user._id
    });

    if (!po) {
      return res.status(404).json({ message: 'PO not found' });
    }

    const machine = po.machines.id(machineId);
    if (!machine) {
      return res.status(404).json({ message: 'Machine not found' });
    }

    // Update the specific stage
    machine[stage] = stageData;

    // Add stage to completed stages if not already present
    if (!machine.completedStages.includes(stage)) {
      machine.completedStages.push(stage);
    }

    // Check if all stages are completed
    const allStages = ['requirement', 'extrusionProduction', 'printing', 'cuttingSealing', 'punch', 'packagingDispatch'];
    machine.isCompleted = allStages.every(s => machine.completedStages.includes(s));

    await po.save();

    res.json({
      message: 'Stage updated successfully',
      machine
    });
  } catch (error) {
    console.error('Update stage error:', error);
    res.status(500).json({ message: 'Failed to update stage' });
  }
});

// Finalize PO
router.put('/:id/finalize', auth, async (req, res) => {
  try {
    const po = await PO.findOne({
      _id: req.params.id,
      createdBy: req.user._id
    });

    if (!po) {
      return res.status(404).json({ message: 'PO not found' });
    }

    po.isFinalized = true;
    po.status = 'completed';
    await po.save();

    res.json({
      message: 'PO finalized successfully',
      po
    });
  } catch (error) {
    console.error('Finalize PO error:', error);
    res.status(500).json({ message: 'Failed to finalize PO' });
  }
});

// Generate PDF for PO
router.get('/:id/pdf', auth, async (req, res) => {
  try {
    const po = await PO.findOne({
      _id: req.params.id,
      createdBy: req.user._id
    });

    if (!po) {
      return res.status(404).json({ message: 'PO not found' });
    }

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${po.poNumber}.pdf"`);

    // Pipe PDF to response
    doc.pipe(res);

    // Add title
    doc.fontSize(20).text(`Purchase Order: ${po.poNumber}`, { align: 'center' });
    doc.fontSize(12).text(`Date: ${po.createdAt.toDateString()}`, { align: 'center' });
    doc.moveDown(2);

    // Add machines data
    for (let i = 1; i <= 6; i++) {
      const machine = po.machines.find(m => m.machineNo === i);

      doc.fontSize(16).text(`Machine ${i}`, { underline: true });
      doc.moveDown(0.5);

      if (machine) {
        // Requirement
        doc.fontSize(14).text('1. Requirement:', { underline: true });
        if (machine.requirement) {
          doc.fontSize(10)
            .text(`Size: ${machine.requirement.size || 'N/A'}`)
            .text(`Micron: ${machine.requirement.micron || 'N/A'}`)
            .text(`Bag Type: ${machine.requirement.bagType || 'N/A'}`)
            .text(`Quantity: ${machine.requirement.quantity || 'N/A'}`)
            .text(`Print: ${machine.requirement.print || 'N/A'}`)
            .text(`Color: ${machine.requirement.color || 'N/A'}`)
            .text(`Packaging Type: ${machine.requirement.packagingType || 'N/A'}`)
            .text(`Material: ${machine.requirement.material || 'N/A'}`);
        } else {
          doc.fontSize(10).text('No data entered');
        }
        doc.moveDown(0.5);

        // Extrusion Production
        doc.fontSize(14).text('2. Extrusion Production:', { underline: true });
        if (machine.extrusionProduction) {
          doc.fontSize(10)
            .text(`Extrusion No.: ${machine.extrusionProduction.extrusionNo || 'N/A'}`)
            .text(`Size: ${machine.extrusionProduction.size || 'N/A'}`)
            .text(`Operator: ${machine.extrusionProduction.operatorName || 'N/A'}`)
            .text(`Ampere: ${machine.extrusionProduction.ampere || 'N/A'}`)
            .text(`Frequency: ${machine.extrusionProduction.frequency || 'N/A'}`)
            .text(`Kgs: ${machine.extrusionProduction.kgs || 'N/A'}`)
            .text(`No. of Rolls: ${machine.extrusionProduction.noOfRolls || 'N/A'}`)
            .text(`Waste: ${machine.extrusionProduction.waste || 'N/A'}`)
            .text(`QC Approved By: ${machine.extrusionProduction.qcApprovedBy || 'N/A'}`)
            .text(`Remark: ${machine.extrusionProduction.remark || 'N/A'}`);
        } else {
          doc.fontSize(10).text('No data entered');
        }
        doc.moveDown(0.5);

        // Continue with other stages...
        // Printing
        doc.fontSize(14).text('3. Printing:', { underline: true });
        if (machine.printing) {
          doc.fontSize(10)
            .text(`Machine No.: ${machine.printing.machineNo || 'N/A'}`)
            .text(`Size: ${machine.printing.size || 'N/A'}`)
            .text(`Operator: ${machine.printing.operatorName || 'N/A'}`)
            .text(`No. of Rolls: ${machine.printing.noOfRolls || 'N/A'}`)
            .text(`Waste: ${machine.printing.waste || 'N/A'}`)
            .text(`Kgs: ${machine.printing.kgs || 'N/A'}`);
        } else {
          doc.fontSize(10).text('No data entered');
        }
        doc.moveDown(0.5);

        // Cutting & Sealing
        doc.fontSize(14).text('4. Cutting & Sealing:', { underline: true });
        if (machine.cuttingSealing) {
          doc.fontSize(10)
            .text(`Machine No.: ${machine.cuttingSealing.machineNo || 'N/A'}`)
            .text(`Size: ${machine.cuttingSealing.size || 'N/A'}`)
            .text(`Operator: ${machine.cuttingSealing.operatorName || 'N/A'}`)
            .text(`Heating 1: ${machine.cuttingSealing.heating1 || 'N/A'}`)
            .text(`Heating 2: ${machine.cuttingSealing.heating2 || 'N/A'}`)
            .text(`No. of Rolls: ${machine.cuttingSealing.noOfRolls || 'N/A'}`)
            .text(`Cutting Waste: ${machine.cuttingSealing.cuttingWaste || 'N/A'}`)
            .text(`Print Waste: ${machine.cuttingSealing.printWaste || 'N/A'}`)
            .text(`Kgs: ${machine.cuttingSealing.kgs || 'N/A'}`);
        } else {
          doc.fontSize(10).text('No data entered');
        }
        doc.moveDown(0.5);

        // Punch
        doc.fontSize(14).text('5. Punch:', { underline: true });
        if (machine.punch) {
          doc.fontSize(10)
            .text(`Machine No.: ${machine.punch.machineNo || 'N/A'}`)
            .text(`Bag Size: ${machine.punch.bagSize || 'N/A'}`)
            .text(`Operator: ${machine.punch.operatorName || 'N/A'}`)
            .text(`Punch Name: ${machine.punch.punchName || 'N/A'}`)
            .text(`Kgs: ${machine.punch.kgs || 'N/A'}`)
            .text(`Waste: ${machine.punch.waste || 'N/A'}`);
        } else {
          doc.fontSize(10).text('No data entered');
        }
        doc.moveDown(0.5);

        // Packaging & Dispatch
        doc.fontSize(14).text('6. Packaging & Dispatch:', { underline: true });
        if (machine.packagingDispatch) {
          doc.fontSize(10)
            .text(`Size: ${machine.packagingDispatch.size || 'N/A'}`)
            .text(`Total Weight: ${machine.packagingDispatch.totalWeight || 'N/A'}`)
            .text(`No. of Rolls: ${machine.packagingDispatch.noOfRolls || 'N/A'}`)
            .text(`No. of Bags: ${machine.packagingDispatch.noOfBags || 'N/A'}`)
            .text(`Challan No.: ${machine.packagingDispatch.challanNo || 'N/A'}`);
        } else {
          doc.fontSize(10).text('No data entered');
        }
      } else {
        doc.fontSize(10).text('Machine not configured');
      }

      doc.moveDown(1);
      if (i < 6) doc.addPage();
    }

    // Finalize PDF
    doc.end();
  } catch (error) {
    console.error('Generate PDF error:', error);
    res.status(500).json({ message: 'Failed to generate PDF' });
  }
});

module.exports = router;
