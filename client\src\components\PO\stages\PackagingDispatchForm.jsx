import React, { useState } from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Grid,
  Alert
} from '@mui/material';

const PackagingDispatchForm = ({ onComplete, onBack, machineData }) => {
  const [formData, setFormData] = useState({
    size: '',
    totalWeight: '',
    noOfRolls: '',
    noOfBags: '',
    challanNo: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    console.log('PackagingDispatchForm - Form data:', formData);

    try {
      const submitData = {
        ...formData,
        totalWeight: formData.totalWeight ? parseFloat(formData.totalWeight) : null,
        noOfRolls: formData.noOfRolls ? parseInt(formData.noOfRolls) : null,
        noOfBags: formData.noOfBags ? parseInt(formData.noOfBags) : null
      };

      console.log('PackagingDispatchForm - Submitting data:', submitData);

      if (onComplete) {
        await onComplete(submitData);
        console.log('PackagingDispatchForm - Data submitted successfully');
      } else {
        throw new Error('onComplete function not provided');
      }
    } catch (error) {
      console.error('PackagingDispatchForm - Submit error:', error);
      setError('Failed to save packaging & dispatch data: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 4 }}>
      <Typography variant="h5" gutterBottom>
        Stage 6: Packaging & Dispatch
      </Typography>
      
      {machineData && (
        <Typography variant="subtitle1" color="primary" gutterBottom>
          Machine {machineData.machineNo}
        </Typography>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Size"
              name="size"
              value={formData.size}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Total Weight"
              name="totalWeight"
              type="number"
              value={formData.totalWeight}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="No. of Rolls"
              name="noOfRolls"
              type="number"
              value={formData.noOfRolls}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="No. of Bags"
              name="noOfBags"
              type="number"
              value={formData.noOfBags}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Challan No."
              name="challanNo"
              value={formData.challanNo}
              onChange={handleChange}
              disabled={loading}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            onClick={onBack}
            disabled={loading}
            size="large"
          >
            Back
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            size="large"
          >
            {loading ? 'Saving...' : 'Complete Machine Entry'}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default PackagingDispatchForm;
