import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Con<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Box,
  Card,
  CardContent,
  Grid,
  AppBar,
  Toolbar,
  IconButton,
  Alert,
  Chip
} from '@mui/material';
import { Add, Search, Logout, Description } from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';

const Dashboard = () => {
  const [pos, setPOs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    fetchPOs();
  }, []);

  const fetchPOs = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/pos');
      setPOs(response.data);
    } catch (error) {
      console.error('Error fetching POs:', error);
      setError('Failed to fetch POs');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`http://localhost:5000/api/pos?search=${searchTerm}`);
      setPOs(response.data);
    } catch (error) {
      console.error('Error searching POs:', error);
      setError('Failed to search POs');
    } finally {
      setLoading(false);
    }
  };

  const createNewPO = async () => {
    try {
      const response = await axios.post('http://localhost:5000/api/pos/create');
      const newPO = response.data.po;
      navigate(`/po/${newPO._id}/workflow`);
    } catch (error) {
      console.error('Error creating PO:', error);
      setError('Failed to create new PO');
    }
  };

  const handlePOClick = (poId) => {
    navigate(`/po/${poId}/detail`);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'draft':
        return 'default';
      case 'in-progress':
        return 'warning';
      case 'completed':
        return 'success';
      default:
        return 'default';
    }
  };

  const filteredPOs = pos.filter(po =>
    po.poNumber.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            PO Management Dashboard
          </Typography>
          <Typography variant="body1" sx={{ mr: 2 }}>
            Welcome, {user?.username}
          </Typography>
          <IconButton color="inherit" onClick={logout}>
            <Logout />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Purchase Orders
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={createNewPO}
            size="large"
          >
            New PO
          </Button>
        </Box>

        <Box display="flex" gap={2} mb={3}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Search by PO number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            InputProps={{
              endAdornment: (
                <IconButton onClick={handleSearch}>
                  <Search />
                </IconButton>
              ),
            }}
          />
        </Box>

        {loading ? (
          <Typography>Loading...</Typography>
        ) : (
          <Grid container spacing={3}>
            {filteredPOs.length === 0 ? (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" align="center" color="textSecondary">
                      {searchTerm ? 'No POs found matching your search' : 'No POs created yet'}
                    </Typography>
                    {!searchTerm && (
                      <Box textAlign="center" mt={2}>
                        <Button
                          variant="contained"
                          startIcon={<Add />}
                          onClick={createNewPO}
                        >
                          Create Your First PO
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ) : (
              filteredPOs.map((po) => (
                <Grid item xs={12} sm={6} md={4} key={po._id}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      '&:hover': {
                        boxShadow: 6,
                      },
                    }}
                    onClick={() => handlePOClick(po._id)}
                  >
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                        <Typography variant="h6" component="h2">
                          {po.poNumber}
                        </Typography>
                        <Chip 
                          label={po.status} 
                          color={getStatusColor(po.status)}
                          size="small"
                        />
                      </Box>
                      <Typography color="textSecondary" gutterBottom>
                        Created: {new Date(po.createdAt).toLocaleDateString()}
                      </Typography>
                      <Typography variant="body2">
                        Machines: {po.machines.length}/6
                      </Typography>
                      {po.isFinalized && (
                        <Chip 
                          label="Finalized" 
                          color="success" 
                          size="small" 
                          sx={{ mt: 1 }}
                        />
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        )}
      </Container>
    </>
  );
};

export default Dashboard;
