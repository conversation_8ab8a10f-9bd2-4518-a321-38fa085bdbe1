import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Contain<PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Box,
  Card,
  CardContent,
  Grid,
  AppBar,
  Toolbar,
  IconButton,
  Alert,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CardActions
} from '@mui/material';
import { Add, Search, Logout, Edit, Visibility, GetApp } from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';

const Dashboard = () => {
  const [pos, setPOs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const { user, logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    fetchPOs();
  }, [statusFilter]);

  const fetchPOs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await axios.get(`http://localhost:5000/api/pos?${params.toString()}`);
      setPOs(response.data);
    } catch (error) {
      console.error('Error fetching POs:', error);
      setError('Failed to fetch POs');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    fetchPOs();
  };

  const createNewPO = async () => {
    try {
      const response = await axios.post('http://localhost:5000/api/pos/create');
      const newPO = response.data.po;
      navigate(`/po/${newPO._id}/stage/requirement`);
    } catch (error) {
      console.error('Error creating PO:', error);
      setError('Failed to create new PO');
    }
  };

  const handleEditPO = (po) => {
    if (po.currentStage === 'completed') {
      setError('PO is already completed and cannot be edited');
      return;
    }
    navigate(`/po/${po._id}/stage/${po.currentStage}`);
  };

  const handleViewPO = (poId) => {
    navigate(`/po/${poId}/detail`);
  };

  const handleDownloadPDF = async (po) => {
    if (po.currentStage !== 'completed') {
      setError('PDF can only be downloaded for completed POs');
      return;
    }

    try {
      const response = await axios.get(`http://localhost:5000/api/pos/${po._id}/pdf`, {
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${po.poNumber}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Error downloading PDF:', error);
      setError('Failed to download PDF');
    }
  };

  const getStageColor = (stage) => {
    switch (stage) {
      case 'requirement':
        return 'info';
      case 'extrusion':
        return 'warning';
      case 'printing':
        return 'secondary';
      case 'cutting':
        return 'primary';
      case 'punch':
        return 'success';
      case 'packaging':
        return 'error';
      case 'completed':
        return 'success';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'draft':
        return 'default';
      case 'in-progress':
        return 'warning';
      case 'completed':
        return 'success';
      default:
        return 'default';
    }
  };

  const filteredPOs = pos.filter(po =>
    po.poNumber.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            PO Management Dashboard
          </Typography>
          <Typography variant="body1" sx={{ mr: 2 }}>
            Welcome, {user?.username}
          </Typography>
          <IconButton color="inherit" onClick={logout}>
            <Logout />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Purchase Orders
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={createNewPO}
            size="large"
          >
            New PO
          </Button>
        </Box>

        <Box display="flex" gap={2} mb={3} flexWrap="wrap">
          <TextField
            sx={{ flex: 1, minWidth: 200 }}
            variant="outlined"
            placeholder="Search by PO number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            slotProps={{
              input: {
                endAdornment: (
                  <IconButton onClick={handleSearch}>
                    <Search />
                  </IconButton>
                ),
              }
            }}
          />
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Filter by Stage</InputLabel>
            <Select
              value={statusFilter}
              label="Filter by Stage"
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <MenuItem value="all">All Stages</MenuItem>
              <MenuItem value="requirement">Requirement</MenuItem>
              <MenuItem value="extrusion">Extrusion</MenuItem>
              <MenuItem value="printing">Printing</MenuItem>
              <MenuItem value="cutting">Cutting & Sealing</MenuItem>
              <MenuItem value="punch">Punch</MenuItem>
              <MenuItem value="packaging">Packaging</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {loading ? (
          <Typography>Loading...</Typography>
        ) : (
          <Grid container spacing={3}>
            {filteredPOs.length === 0 ? (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" align="center" color="textSecondary">
                      {searchTerm ? 'No POs found matching your search' : 'No POs created yet'}
                    </Typography>
                    {!searchTerm && (
                      <Box textAlign="center" mt={2}>
                        <Button
                          variant="contained"
                          startIcon={<Add />}
                          onClick={createNewPO}
                        >
                          Create Your First PO
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ) : (
              filteredPOs.map((po) => (
                <Grid item xs={12} sm={6} md={4} key={po._id}>
                  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                        <Typography variant="h6" component="h2">
                          {po.poNumber}
                        </Typography>
                        <Chip
                          label={po.status}
                          color={getStatusColor(po.status)}
                          size="small"
                        />
                      </Box>

                      <Typography color="textSecondary" gutterBottom>
                        Created: {new Date(po.createdAt).toLocaleDateString()}
                      </Typography>

                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2">
                          Machines: {po.machines.length}/6
                        </Typography>
                        <Chip
                          label={po.currentStageDisplay || po.currentStage}
                          color={getStageColor(po.currentStage)}
                          size="small"
                        />
                      </Box>

                      {po.isFinalized && (
                        <Chip
                          label="Finalized"
                          color="success"
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      )}
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                      <Box>
                        {po.currentStage !== 'completed' && (
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<Edit />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditPO(po);
                            }}
                          >
                            Edit
                          </Button>
                        )}
                      </Box>

                      <Box display="flex" gap={1}>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<Visibility />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewPO(po._id);
                          }}
                        >
                          View
                        </Button>

                        {po.currentStage === 'completed' && (
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<GetApp />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDownloadPDF(po);
                            }}
                          >
                            PDF
                          </Button>
                        )}
                      </Box>
                    </CardActions>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        )}
      </Container>
    </>
  );
};

export default Dashboard;
