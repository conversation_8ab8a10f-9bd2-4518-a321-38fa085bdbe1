const mongoose = require('mongoose');

// Requirement Schema
const requirementSchema = new mongoose.Schema({
  machineNo: {
    type: Number,
    required: true,
    min: 1,
    max: 6
  },
  size: String,
  micron: String,
  bagType: String,
  quantity: Number,
  print: String,
  color: String,
  packagingType: String,
  material: String,
  image: String // File path for uploaded image
});

// Extrusion Production Schema
const extrusionProductionSchema = new mongoose.Schema({
  extrusionNo: String,
  size: String,
  operatorName: String,
  ampere: Number,
  frequency: Number,
  kgs: Number,
  noOfRolls: Number,
  waste: Number,
  qcApprovedBy: String,
  remark: String
});

// Printing Schema
const printingSchema = new mongoose.Schema({
  machineNo: Number,
  size: String,
  operatorName: String,
  noOfRolls: Number,
  waste: Number,
  kgs: Number
});

// Cutting & Sealing Schema
const cuttingSealingSchema = new mongoose.Schema({
  machineNo: Number,
  size: String,
  operatorName: String,
  heating1: Number,
  heating2: Number,
  noOfRolls: Number,
  cuttingWaste: Number,
  printWaste: Number,
  kgs: Number
});

// Punch Schema
const punchSchema = new mongoose.Schema({
  machineNo: Number,
  bagSize: String,
  operatorName: String,
  punchName: String,
  kgs: Number,
  waste: Number
});

// Packaging & Dispatch Schema
const packagingDispatchSchema = new mongoose.Schema({
  size: String,
  totalWeight: Number,
  noOfRolls: Number,
  noOfBags: Number,
  challanNo: String
});

// Machine Entry Schema (contains all 6 stages)
const machineEntrySchema = new mongoose.Schema({
  machineNo: {
    type: Number,
    required: true,
    min: 1,
    max: 6
  },
  requirement: requirementSchema,
  extrusionProduction: extrusionProductionSchema,
  printing: printingSchema,
  cuttingSealing: cuttingSealingSchema,
  punch: punchSchema,
  packagingDispatch: packagingDispatchSchema,
  completedStages: {
    type: [String],
    default: []
  },
  isCompleted: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Main PO Schema
const poSchema = new mongoose.Schema({
  poNumber: {
    type: String,
    required: true,
    unique: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  machines: [machineEntrySchema],
  status: {
    type: String,
    enum: ['draft', 'in-progress', 'completed'],
    default: 'draft'
  },
  isFinalized: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Method to get next available machine numbers
poSchema.methods.getAvailableMachineNumbers = function() {
  const usedMachines = this.machines.map(machine => machine.machineNo);
  const allMachines = [1, 2, 3, 4, 5, 6];
  return allMachines.filter(num => !usedMachines.includes(num));
};

// Method to check if PO can accept more machines
poSchema.methods.canAddMoreMachines = function() {
  return this.machines.length < 6;
};

module.exports = mongoose.model('PO', poSchema);
